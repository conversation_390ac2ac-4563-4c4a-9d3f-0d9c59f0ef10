<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FASTQ Alignment Job Details</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sequence-display {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 4px;
            word-break: break-all;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .status-badge {
            font-size: 0.9rem;
            padding: 0.5rem 1rem;
        }
        .status-pending { background-color: #ffc107; }
        .status-running { background-color: #17a2b8; }
        .status-completed { background-color: #28a745; }
        .status-failed { background-color: #dc3545; }
        .loading-container {
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        #downloadDropdown {
            display: none; /* Initially hidden, shown when results are available */
        }
        .dropdown-item i {
            width: 16px;
            margin-right: 8px;
        }
        .mutation-highlight {
            background-color: #ffeb3b;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
        .insertion-highlight {
            background-color: #4caf50;
            color: white;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
        .deletion-highlight {
            background-color: #f44336;
            color: white;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
        .sequence-card {
            border-left: 4px solid #007bff;
            margin-bottom: 1rem;
        }
        .sequence-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        .alignment-display {
            font-family: 'Courier New', monospace;
            font-size: 11px;
            background-color: #f8f9fa;
            padding: 0.5rem;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre;
            line-height: 1.4;
            border: 1px solid #e9ecef;
        }

        /* Histogram styles */
        .histogram-container {
            min-height: 200px;
            padding: 10px 10px 45px 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #e9ecef;
            position: relative;
        }

        .histogram {
            display: flex;
            align-items: flex-end;
            height: 150px;
            gap: 2px;
            margin-bottom: 10px;
            border-bottom: 1px solid #dee2e6;
        }

        .histogram-bar-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            height: 100%;
            position: relative;
            cursor: pointer;
            justify-content: flex-end;
        }

        .histogram-bar {
            width: 100%;
            min-height: 2px;
            border-radius: 2px 2px 0 0;
            display: flex;
            align-items: flex-start;
            justify-content: center;
            transition: opacity 0.2s ease;
            position: relative;
            margin-bottom: 0;
        }

        .histogram-bar:hover {
            opacity: 0.8;
        }

        .histogram-count {
            color: white;
            font-size: 10px;
            /* font-weight: bold; */
            text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
            padding: 2px;
            position: absolute;
            top: -18px;
            left: 50%;
            transform: translateX(-50%);
        }

        .histogram-label {
            font-size: 9px;
            color: #666;
            text-align: center;
            white-space: nowrap;
            max-width: 80px;
            overflow: hidden;
            text-overflow: ellipsis;
            position: absolute;
            bottom: -35px;
            left: 50%;
            transform: translateX(-50%) rotate(-45deg);
            transform-origin: center;
        }

        .histogram-stats {
            font-size: 11px;
            padding: 8px;
            background-color: #ffffff;
            border-radius: 3px;
            border: 1px solid #e9ecef;
            position: relative;
            z-index: 10;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1><i class="fas fa-info-circle text-primary"></i> FASTQ Alignment Job Details</h1>
                        <p class="text-muted">Detailed information and results for FASTQ alignment job</p>
                    </div>
                    <div>
                        <a href="/fastq-alignment" class="btn btn-outline-secondary me-2">
                            <i class="fas fa-arrow-left"></i> Back to FASTQ Alignment
                        </a>
                        <a href="/" class="btn btn-outline-secondary">
                            <i class="fas fa-home"></i> Home
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading State -->
        <div id="loadingContainer" class="loading-container">
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading job details...</span>
                </div>
                <p class="mt-3">Loading job details...</p>
            </div>
        </div>

        <!-- Error State -->
        <div id="errorContainer" class="d-none">
            <div class="alert alert-danger">
                <h4><i class="fas fa-exclamation-triangle"></i> Error</h4>
                <p id="errorMessage">Failed to load job details</p>
                <button class="btn btn-outline-danger" onclick="loadJobDetails()">
                    <i class="fas fa-retry"></i> Retry
                </button>
            </div>
        </div>

        <!-- Job Details Content -->
        <div id="jobDetailsContainer" class="d-none">
            <!-- Job Information -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-info-circle"></i> Job Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Job Name:</strong> <span id="jobName">-</span></p>
                                    <p><strong>Job ID:</strong> <code id="jobId">-</code></p>
                                    <p><strong>Status:</strong> <span id="jobStatus">-</span></p>
                                    <p><strong>Filename:</strong> <span id="jobFilename">-</span></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Created:</strong> <span id="jobCreated">-</span></p>
                                    <p><strong>Completed:</strong> <span id="jobCompleted">-</span></p>
                                    <p><strong>Duration:</strong> <span id="jobDuration">-</span></p>
                                    <p><strong>Sequences Processed:</strong> <span id="sequenceCount">-</span></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Reference Sequence -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-dna"></i> Reference Sequence</h5>
                        </div>
                        <div class="card-body">
                            <div class="sequence-display" id="referenceSequence">-</div>
                            <p class="text-muted small mt-2">
                                <strong>Length:</strong> <span id="referenceLength">-</span> nucleotides
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Alignment Parameters -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-sliders-h"></i> Alignment Parameters</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <p><strong>Match Score:</strong> <span id="matchScore">-</span></p>
                                </div>
                                <div class="col-md-3">
                                    <p><strong>Mismatch Score:</strong> <span id="mismatchScore">-</span></p>
                                </div>
                                <div class="col-md-3">
                                    <p><strong>Open Gap Score:</strong> <span id="openGapScore">-</span></p>
                                </div>
                                <div class="col-md-3">
                                    <p><strong>Extend Gap Score:</strong> <span id="extendGapScore">-</span></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filter and Sort Controls -->
            <div class="filter-controls mt-3" id="filterSortControls" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-filter"></i> Filter & Sort Sequences</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-2">
                                <label for="mutationFilter" class="form-label">Mutations:</label>
                                <select class="form-select form-select-sm" id="mutationFilter">
                                    <option value="all">All Sequences</option>
                                    <option value="mutated">With Mutations</option>
                                    <option value="no-mutations">No Mutations</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="insertionFilter" class="form-label">Insertions:</label>
                                <select class="form-select form-select-sm" id="insertionFilter">
                                    <option value="all">All</option>
                                    <option value="with-insertions">With Insertions</option>
                                    <option value="no-insertions">No Insertions</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="deletionFilter" class="form-label">Deletions:</label>
                                <select class="form-select form-select-sm" id="deletionFilter">
                                    <option value="all">All</option>
                                    <option value="with-deletions">With Deletions</option>
                                    <option value="no-deletions">No Deletions</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="scoreFilter" class="form-label">Min Score:</label>
                                <input type="number" class="form-control form-control-sm" id="scoreFilter" placeholder="e.g., 1000">
                            </div>
                            <div class="col-md-2">
                                <label for="sortBy" class="form-label">Sort by:</label>
                                <select class="form-select form-select-sm" id="sortBy">
                                    <option value="original">Original Order</option>
                                    <option value="score-desc">Score (High to Low)</option>
                                    <option value="score-asc">Score (Low to High)</option>
                                    <option value="mutations-desc">Mutations (Most to Least)</option>
                                    <option value="mutations-asc">Mutations (Least to Most)</option>
                                    <option value="name-asc">Name (A to Z)</option>
                                </select>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button class="btn btn-sm btn-secondary" id="resetFilters">
                                    <i class="fas fa-undo"></i> Reset
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Results Count -->
            <div class="mb-2" id="resultsCount" style="display: none;">
                <small class="text-muted">Showing <span id="filteredCount">0</span> of <span id="totalCount">0</span> sequences</small>
            </div>

            <!-- Results Section -->
            <div id="resultsContainer" class="d-none">
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-download"></i> Download Results</h5>
                            </div>
                            <div class="card-body text-center">
                                <p class="text-muted mb-4">Download the complete alignment results for this job.</p>
                                <button class="btn btn-success btn-lg" onclick="downloadResults()">
                                    <i class="fas fa-download"></i> Download JSON Results
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- No Results -->
            <div id="noResultsContainer" class="d-none">
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="alert alert-warning">
                            <h4><i class="fas fa-exclamation-triangle"></i> No Results Available</h4>
                            <p>This job has not completed yet or no results are available for download.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="static/path-utils.js"></script>
    <script src="static/fastp-job-details.js"></script>
</body>
</html>
