"""
FASTP Results Storage

This module handles file-based storage for FASTP alignment job results.
Similar to BLAST storage but adapted for FASTP alignment data.
"""

import os
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class FastpResultsStorage:
    """Manages file-based storage for FASTP alignment job results"""
    
    def __init__(self, base_dir: Optional[str] = None):
        """
        Initialize the storage manager
        
        Args:
            base_dir: Base directory for storing results. If None, uses environment variable
                     FASTP_RESULTS_DIR or defaults to './data/fastp_results'
        """
        if base_dir is None:
            base_dir = os.getenv("FASTP_RESULTS_DIR", "./data/fastp_results")
        
        self.base_dir = Path(base_dir)
        self.results_dir = self.base_dir / "results"
        self.metadata_dir = self.base_dir / "metadata"
        
        # Create directories if they don't exist
        self.results_dir.mkdir(parents=True, exist_ok=True)
        self.metadata_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"FastpResultsStorage initialized with base directory: {self.base_dir}")
    
    def save_job_result(self, job_id: str, result_data: Dict[Any, Any]) -> str:
        """
        Save FASTP job result data to a file
        
        Args:
            job_id: Unique job identifier
            result_data: Complete result data from FASTP alignment
            
        Returns:
            Path to the saved result file
        """
        try:
            result_file = self.results_dir / f"{job_id}.json"
            
            # Add metadata to result data
            result_data_with_meta = {
                "job_id": job_id,
                "saved_at": datetime.now().isoformat(),
                "file_version": "1.0",
                "data": result_data
            }
            
            # Save to file with pretty formatting
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(result_data_with_meta, f, indent=2, default=str, ensure_ascii=False)
            
            logger.info(f"Saved FASTP results for job {job_id} to {result_file}")
            return str(result_file)
            
        except Exception as e:
            logger.error(f"Failed to save FASTP results for job {job_id}: {e}")
            raise
    
    def load_job_result(self, job_id: str) -> Optional[Dict[Any, Any]]:
        """
        Load FASTP job result data from file
        
        Args:
            job_id: Unique job identifier
            
        Returns:
            Result data dictionary or None if file doesn't exist
        """
        try:
            result_file = self.results_dir / f"{job_id}.json"
            
            if not result_file.exists():
                logger.warning(f"Result file not found for job {job_id}")
                return None
            
            with open(result_file, 'r', encoding='utf-8') as f:
                file_data = json.load(f)
            
            # Return just the data portion, not the metadata wrapper
            return file_data.get("data", file_data)
            
        except Exception as e:
            logger.error(f"Failed to load FASTP results for job {job_id}: {e}")
            return None
    
    def get_result_file_path(self, job_id: str) -> Optional[str]:
        """
        Get the file path for a job's results
        
        Args:
            job_id: Unique job identifier
            
        Returns:
            File path string or None if file doesn't exist
        """
        result_file = self.results_dir / f"{job_id}.json"
        return str(result_file) if result_file.exists() else None
    
    def delete_job_result(self, job_id: str) -> bool:
        """
        Delete FASTP job result file
        
        Args:
            job_id: Unique job identifier
            
        Returns:
            True if file was deleted or didn't exist, False if error occurred
        """
        try:
            result_file = self.results_dir / f"{job_id}.json"
            
            if result_file.exists():
                result_file.unlink()
                logger.info(f"Deleted FASTP result file for job {job_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete FASTP result file for job {job_id}: {e}")
            return False
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """
        Get storage statistics
        
        Returns:
            Dictionary with storage statistics
        """
        try:
            result_files = list(self.results_dir.glob("*.json"))
            total_files = len(result_files)
            
            # Calculate total size
            total_size = sum(f.stat().st_size for f in result_files if f.exists())
            
            return {
                "total_files": total_files,
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "results_directory": str(self.results_dir),
                "metadata_directory": str(self.metadata_dir)
            }
            
        except Exception as e:
            logger.error(f"Failed to get storage stats: {e}")
            return {
                "total_files": 0,
                "total_size_bytes": 0,
                "total_size_mb": 0,
                "error": str(e)
            }
    
    def cleanup_old_results(self, days_old: int = 30) -> Dict[str, Any]:
        """
        Clean up result files older than specified days
        
        Args:
            days_old: Number of days after which files should be considered old
            
        Returns:
            Dictionary with cleanup statistics
        """
        try:
            cutoff_time = datetime.now().timestamp() - (days_old * 24 * 60 * 60)
            result_files = list(self.results_dir.glob("*.json"))
            
            deleted_count = 0
            deleted_size = 0
            
            for file_path in result_files:
                if file_path.stat().st_mtime < cutoff_time:
                    file_size = file_path.stat().st_size
                    file_path.unlink()
                    deleted_count += 1
                    deleted_size += file_size
                    logger.info(f"Deleted old FASTP result file: {file_path}")
            
            return {
                "deleted_files": deleted_count,
                "deleted_size_bytes": deleted_size,
                "deleted_size_mb": round(deleted_size / (1024 * 1024), 2),
                "cutoff_days": days_old
            }
            
        except Exception as e:
            logger.error(f"Failed to cleanup old results: {e}")
            return {
                "deleted_files": 0,
                "deleted_size_bytes": 0,
                "deleted_size_mb": 0,
                "error": str(e)
            }


# Global instance for easy access
fastp_storage = FastpResultsStorage()


def save_job_result(job_id: str, result_data: Dict[Any, Any]) -> str:
    """Convenience function to save job result"""
    return fastp_storage.save_job_result(job_id, result_data)


def load_job_result(job_id: str) -> Optional[Dict[Any, Any]]:
    """Convenience function to load job result"""
    return fastp_storage.load_job_result(job_id)


def delete_job_result(job_id: str) -> bool:
    """Convenience function to delete job result"""
    return fastp_storage.delete_job_result(job_id)


def get_storage_stats() -> Dict[str, Any]:
    """Convenience function to get storage stats"""
    return fastp_storage.get_storage_stats()


def cleanup_old_results(days_old: int = 30) -> Dict[str, Any]:
    """Convenience function to cleanup old results"""
    return fastp_storage.cleanup_old_results(days_old)
